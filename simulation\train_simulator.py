"""
Interactive Train Movement Simulator
Real-time visualization and scenario testing for railway traffic control
"""

import pygame
import numpy as np
import time
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import queue
from datetime import datetime, timedelta

from models.railway_network import RailwayNetwork, Station, Track
from models.ai_optimizer import Train, TrafficState, HybridOptimizer, TrainStatus, TrainPriority


class SimulationMode(Enum):
    REAL_TIME = "real_time"
    ACCELERATED = "accelerated"
    STEP_BY_STEP = "step_by_step"


@dataclass
class SimulationConfig:
    """Simulation configuration parameters"""
    mode: SimulationMode = SimulationMode.REAL_TIME
    speed_multiplier: float = 1.0
    auto_optimize: bool = True
    show_delays: bool = True
    show_capacity: bool = True
    record_metrics: bool = True


class TrainSimulator:
    """Core train movement simulation engine"""
    
    def __init__(self, network: RailwayNetwork, config: SimulationConfig = None):
        self.network = network
        self.config = config or SimulationConfig()
        self.traffic_state = TrafficState(network)
        self.optimizer = HybridOptimizer(network)
        
        # Simulation state
        self.current_time = 0.0
        self.is_running = False
        self.is_paused = False
        self.step_size = 0.1  # minutes
        
        # Event system
        self.event_queue = queue.Queue()
        self.event_handlers = {}
        
        # Metrics collection
        self.metrics_history = []
        self.performance_data = {
            'throughput': [],
            'average_delay': [],
            'capacity_utilization': [],
            'optimization_calls': 0
        }
        
        # Visual state for rendering
        self.train_positions = {}  # train_id -> (lat, lon, progress)
        self.train_animations = {}  # train_id -> animation state
        
    def add_train(self, train: Train):
        """Add train to simulation"""
        self.traffic_state.add_train(train)
        self.train_positions[train.id] = self._get_initial_position(train)
        self.train_animations[train.id] = {
            'target_position': None,
            'animation_progress': 0.0,
            'moving': False
        }
        
    def _get_initial_position(self, train: Train) -> Tuple[float, float, float]:
        """Get initial position for train"""
        if train.route and train.current_position < len(train.route):
            station_id = train.route[train.current_position]
            station = self.network.stations[station_id]
            return (station.latitude, station.longitude, 0.0)
        return (0.0, 0.0, 0.0)
    
    def start_simulation(self):
        """Start the simulation"""
        self.is_running = True
        self.is_paused = False
        
        # Start simulation thread
        self.sim_thread = threading.Thread(target=self._simulation_loop)
        self.sim_thread.daemon = True
        self.sim_thread.start()
        
        self._emit_event('simulation_started', {'time': self.current_time})
    
    def pause_simulation(self):
        """Pause/unpause simulation"""
        self.is_paused = not self.is_paused
        self._emit_event('simulation_paused' if self.is_paused else 'simulation_resumed', 
                        {'time': self.current_time})
    
    def stop_simulation(self):
        """Stop the simulation"""
        self.is_running = False
        self._emit_event('simulation_stopped', {'time': self.current_time})
    
    def step_simulation(self):
        """Advance simulation by one step"""
        if not self.is_running:
            self.start_simulation()
        
        self._update_simulation()
        self._emit_event('simulation_stepped', {'time': self.current_time})
    
    def _simulation_loop(self):
        """Main simulation loop"""
        last_time = time.time()
        
        while self.is_running:
            current_real_time = time.time()
            dt = current_real_time - last_time
            last_time = current_real_time
            
            if not self.is_paused:
                # Update simulation time based on speed multiplier
                if self.config.mode == SimulationMode.REAL_TIME:
                    self.current_time += dt * self.config.speed_multiplier / 60.0  # Convert to minutes
                elif self.config.mode == SimulationMode.ACCELERATED:
                    self.current_time += self.step_size * self.config.speed_multiplier
                
                self._update_simulation()
            
            # Control simulation speed
            if self.config.mode == SimulationMode.STEP_BY_STEP:
                break
            
            time.sleep(0.1)  # 10 FPS update rate
    
    def _update_simulation(self):
        """Update simulation state"""
        # Update train positions and status
        for train_id, train in self.traffic_state.trains.items():
            self._update_train(train)
        
        # Run AI optimization if enabled
        if self.config.auto_optimize and self.current_time % 5.0 < self.step_size:  # Every 5 minutes
            self._run_optimization()
        
        # Update metrics
        if self.config.record_metrics:
            self._update_metrics()
        
        # Emit update event
        self._emit_event('simulation_updated', {
            'time': self.current_time,
            'trains': {tid: self._serialize_train(train) 
                      for tid, train in self.traffic_state.trains.items()},
            'metrics': self._get_current_metrics()
        })
    
    def _update_train(self, train: Train):
        """Update individual train state"""
        if train.status == TrainStatus.COMPLETED:
            return
        
        # Check if train should move to next station
        if (train.current_position < len(train.scheduled_times) and 
            self.current_time >= train.scheduled_times[train.current_position] + train.delay):
            
            if train.current_position + 1 < len(train.route):
                # Move to next station
                self._move_train_to_next_station(train)
            else:
                # Train completed journey
                train.status = TrainStatus.COMPLETED
                self._emit_event('train_completed', {'train_id': train.id, 'time': self.current_time})
    
    def _move_train_to_next_station(self, train: Train):
        """Move train to next station in route"""
        current_station = train.route[train.current_position]
        next_station = train.route[train.current_position + 1]

        # Find the correct track ID - check both directions
        track_id = None
        for tid, track in self.network.tracks.items():
            if ((track.from_station == current_station and track.to_station == next_station) or
                (track.from_station == next_station and track.to_station == current_station)):
                track_id = tid
                break

        if not track_id:
            # Create a virtual track ID for simulation
            track_id = f"{current_station}-{next_station}"
            # Initialize track occupancy if not exists
            if track_id not in self.traffic_state.track_occupancy:
                self.traffic_state.track_occupancy[track_id] = None

        # Check track availability
        if track_id in self.traffic_state.track_occupancy:
            if self.traffic_state.track_occupancy[track_id] is None:
                # Track is free, move train
                self.traffic_state.track_occupancy[track_id] = train.id
                train.current_position += 1
                train.status = TrainStatus.RUNNING

                # Update visual position
                self._update_train_visual_position(train)

                # Calculate travel time
                if track_id in self.network.tracks:
                    track = self.network.tracks[track_id]
                    travel_time = track.distance / track.max_speed * 60  # Convert to minutes
                    # Add delay for congestion, gradient, etc.
                    delay_factor = 1.0 + (track.gradient / 100.0) * 0.1
                    travel_time *= delay_factor
                else:
                    # Default travel time for virtual tracks
                    travel_time = 30.0  # 30 minutes default

                # Release track after travel time
                threading.Timer(travel_time / self.config.speed_multiplier,
                              self._release_track, args=[track_id]).start()

                self._emit_event('train_moved', {
                    'train_id': train.id,
                    'from_station': current_station,
                    'to_station': next_station,
                    'time': self.current_time
                })
            else:
                # Track occupied, add delay
                train.delay += self.step_size
                train.status = TrainStatus.DELAYED
                self._emit_event('train_delayed', {
                    'train_id': train.id,
                    'delay': train.delay,
                    'reason': 'track_occupied',
                    'time': self.current_time
                })
    
    def _release_track(self, track_id: str):
        """Release track occupancy"""
        if track_id in self.traffic_state.track_occupancy:
            train_id = self.traffic_state.track_occupancy[track_id]
            self.traffic_state.track_occupancy[track_id] = None
            
            if train_id:
                self._emit_event('track_released', {
                    'track_id': track_id,
                    'train_id': train_id,
                    'time': self.current_time
                })
    
    def _update_train_visual_position(self, train: Train):
        """Update train visual position for animation"""
        if train.current_position < len(train.route):
            station_id = train.route[train.current_position]
            station = self.network.stations[station_id]
            
            # Animate movement between stations
            if train.current_position > 0:
                prev_station_id = train.route[train.current_position - 1]
                prev_station = self.network.stations[prev_station_id]
                
                # Linear interpolation between stations
                progress = 0.0  # Will be updated by animation
                lat = prev_station.latitude + (station.latitude - prev_station.latitude) * progress
                lon = prev_station.longitude + (station.longitude - prev_station.longitude) * progress
                
                self.train_positions[train.id] = (lat, lon, progress)
                self.train_animations[train.id] = {
                    'target_position': (station.latitude, station.longitude),
                    'animation_progress': 0.0,
                    'moving': True
                }
            else:
                self.train_positions[train.id] = (station.latitude, station.longitude, 1.0)
    
    def _run_optimization(self):
        """Run AI optimization and apply results"""
        try:
            print(f"🧠 Running AI optimization at time {self.current_time:.1f}")
            result = self.optimizer.optimize_traffic(self.traffic_state)
            self.performance_data['optimization_calls'] += 1

            # Log optimization results
            if result['milp_solution']['status'] == 'Optimal':
                improvement = result['milp_solution'].get('metrics', {}).get('improvement', 0)
                print(f"✅ Optimization completed - Delay improvement: {improvement:.1f} minutes")

                # Apply any additional optimization effects
                self._apply_optimization_effects(result)
            else:
                print(f"⚠️ Optimization status: {result['milp_solution']['status']}")

            self._emit_event('optimization_completed', {
                'result': result,
                'time': self.current_time,
                'trains_affected': len(result.get('applied_changes', {}))
            })
        except Exception as e:
            print(f"❌ Optimization failed: {str(e)}")
            self._emit_event('optimization_failed', {
                'error': str(e),
                'time': self.current_time
            })

    def _apply_optimization_effects(self, optimization_result):
        """Apply additional optimization effects to simulation"""
        if 'applied_changes' not in optimization_result:
            return

        changes_applied = 0
        for train_id, changes in optimization_result['applied_changes'].items():
            if train_id in self.traffic_state.trains:
                train = self.traffic_state.trains[train_id]

                # Apply optimized delay
                if 'optimized_delay' in changes:
                    old_delay = train.delay
                    train.delay = changes['optimized_delay']
                    if old_delay != train.delay:
                        changes_applied += 1
                        print(f"🔧 Train {train_id}: delay {old_delay:.1f} → {train.delay:.1f} min")

        if changes_applied > 0:
            print(f"✅ Applied optimization to {changes_applied} trains")
    
    def _update_metrics(self):
        """Update performance metrics"""
        metrics = self._get_current_metrics()
        self.metrics_history.append({
            'time': self.current_time,
            **metrics
        })
        
        # Update performance data arrays
        self.performance_data['throughput'].append(metrics['throughput'])
        self.performance_data['average_delay'].append(metrics['average_delay'])
        self.performance_data['capacity_utilization'].append(metrics['capacity_utilization'])
        
        # Keep only last 1000 data points
        for key in ['throughput', 'average_delay', 'capacity_utilization']:
            if len(self.performance_data[key]) > 1000:
                self.performance_data[key] = self.performance_data[key][-1000:]
    
    def _get_current_metrics(self) -> Dict[str, float]:
        """Get current performance metrics"""
        trains = list(self.traffic_state.trains.values())
        
        if not trains:
            return {
                'throughput': 0.0,
                'average_delay': 0.0,
                'capacity_utilization': 0.0,
                'active_trains': 0
            }
        
        completed_trains = [t for t in trains if t.status == TrainStatus.COMPLETED]
        active_trains = [t for t in trains if t.status in [TrainStatus.RUNNING, TrainStatus.DELAYED]]
        
        throughput = len(completed_trains)
        average_delay = np.mean([t.delay for t in trains]) if trains else 0.0
        
        # Calculate capacity utilization
        total_capacity = sum(station.capacity for station in self.network.stations.values())
        used_capacity = sum(len(occupants) for occupants in self.traffic_state.station_occupancy.values())
        capacity_utilization = used_capacity / total_capacity if total_capacity > 0 else 0.0
        
        return {
            'throughput': throughput,
            'average_delay': average_delay,
            'capacity_utilization': capacity_utilization,
            'active_trains': len(active_trains)
        }
    
    def _serialize_train(self, train: Train) -> Dict:
        """Serialize train for JSON output"""
        return {
            'id': train.id,
            'route': train.route,
            'priority': train.priority.value,
            'current_position': train.current_position,
            'current_time': train.current_time,
            'delay': train.delay,
            'status': train.status.value,
            'visual_position': self.train_positions.get(train.id, (0, 0, 0))
        }
    
    def _emit_event(self, event_type: str, data: Dict):
        """Emit simulation event"""
        event = {
            'type': event_type,
            'timestamp': datetime.now().isoformat(),
            'simulation_time': self.current_time,
            'data': data
        }
        
        self.event_queue.put(event)
        
        # Call registered handlers
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(event)
                except Exception as e:
                    print(f"Error in event handler: {e}")
    
    def register_event_handler(self, event_type: str, handler):
        """Register event handler"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    def get_events(self) -> List[Dict]:
        """Get all pending events"""
        events = []
        while not self.event_queue.empty():
            try:
                events.append(self.event_queue.get_nowait())
            except queue.Empty:
                break
        return events
    
    def create_scenario(self, scenario_name: str, trains: List[Train] = None,
                       disruptions: List[Dict] = None) -> Dict:
        """Create a simulation scenario with predefined or custom trains"""

        # If no trains provided, generate based on scenario type
        if trains is None:
            trains = self._generate_scenario_trains(scenario_name)

        scenario = {
            'name': scenario_name,
            'created_at': datetime.now().isoformat(),
            'trains': [asdict(train) for train in trains],
            'disruptions': disruptions or [],
            'network_stats': self.network.get_network_stats()
        }

        return scenario

    def _generate_scenario_trains(self, scenario_name: str) -> List[Train]:
        """Generate trains based on scenario type"""
        from models.ai_optimizer import Train, TrainPriority, TrainStatus

        trains = []

        if scenario_name == "rush_hour":
            # High traffic scenario
            train_configs = [
                ("12951", ["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"], TrainPriority.EXPRESS, [0, 0.5, 10.5, 14.5, 19.5, 29.5, 34.5, 42.5]),
                ("12952", ["CSTM", "KYN", "NK", "MMR", "BSL", "NGP", "BPL", "JHS", "ALD", "GZB", "DEL"], TrainPriority.EXPRESS, [0, 1.0, 3.0, 5.5, 8.5, 12.5, 22.5, 27.5, 31.5, 42.0, 42.5]),
                ("19037", ["DEL", "AGC", "GWL", "JHS", "BPL"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5, 8.5, 13.5]),
                ("19038", ["BPL", "ET", "NGP", "BSL", "MMR"], TrainPriority.PASSENGER, [0, 2.0, 4.5, 8.5, 12.5]),
                ("50001", ["DEL", "GZB", "ALD", "JHS"], TrainPriority.FREIGHT, [2.0, 2.5, 12.5, 16.5]),
                ("50002", ["NGP", "WR", "BSL", "MMR"], TrainPriority.FREIGHT, [1.0, 3.5, 7.5, 11.5]),
                ("12953", ["PUNE", "LNL", "IGP", "NK", "MMR"], TrainPriority.EXPRESS, [0, 1.5, 3.5, 5.5, 7.5]),
                ("19039", ["KYN", "LNL", "PUNE"], TrainPriority.PASSENGER, [0, 2.0, 4.0])
            ]

        elif scenario_name == "disruption":
            # Track disruption scenario with delays
            train_configs = [
                ("12951", ["DEL", "GZB", "ALD", "JHS", "BPL"], TrainPriority.EXPRESS, [0, 0.5, 10.5, 14.5, 19.5]),
                ("19037", ["DEL", "AGC", "GWL", "JHS"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5, 8.5]),
                ("50001", ["BPL", "ET", "NGP"], TrainPriority.FREIGHT, [0, 2.0, 6.0]),
                ("87001", ["NGP", "BSL", "MMR"], TrainPriority.MAINTENANCE, [2.0, 6.0, 10.0]),
                ("12952", ["CSTM", "KYN", "LNL", "PUNE"], TrainPriority.EXPRESS, [0, 1.0, 3.0, 5.0])
            ]

        else:  # normal operations
            train_configs = [
                ("12951", ["DEL", "GZB", "ALD", "JHS", "BPL", "NGP"], TrainPriority.EXPRESS, [0, 0.5, 10.5, 14.5, 19.5, 29.5]),
                ("12952", ["CSTM", "KYN", "NK", "MMR", "BSL"], TrainPriority.EXPRESS, [0, 1.0, 3.0, 5.5, 8.5]),
                ("19037", ["DEL", "AGC", "GWL", "JHS"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5, 8.5]),
                ("50001", ["NGP", "BSL", "MMR"], TrainPriority.FREIGHT, [0, 4.0, 8.0]),
                ("19038", ["PUNE", "LNL", "KYN", "CSTM"], TrainPriority.PASSENGER, [0, 1.5, 3.5, 5.5]),
                ("87001", ["BPL", "ET", "NGP"], TrainPriority.MAINTENANCE, [3.0, 5.0, 9.0])
            ]

        # Create train objects
        for train_id, route, priority, scheduled_times in train_configs:
            delay = 0.0
            if scenario_name == "disruption":
                delay = random.uniform(0, 15)  # Random delays for disruption scenario
            elif scenario_name == "rush_hour":
                delay = random.uniform(0, 8)   # Some delays for rush hour

            train = Train(
                id=train_id,
                route=route,
                priority=priority,
                scheduled_times=scheduled_times,
                current_position=0,
                current_time=0.0,
                delay=delay,
                status=TrainStatus.SCHEDULED
            )
            trains.append(train)

        return trains
    
    def load_scenario(self, scenario: Dict):
        """Load a simulation scenario"""
        # Clear current state
        self.traffic_state = TrafficState(self.network)
        self.train_positions = {}
        self.train_animations = {}
        self.current_time = 0.0
        
        # Load trains
        for train_data in scenario['trains']:
            train_data['priority'] = TrainPriority(train_data['priority'])
            train_data['status'] = TrainStatus(train_data['status'])
            train = Train(**train_data)
            self.add_train(train)
        
        self._emit_event('scenario_loaded', {
            'scenario_name': scenario['name'],
            'train_count': len(scenario['trains'])
        })
    
    def export_metrics(self, filepath: str):
        """Export metrics to JSON file"""
        export_data = {
            'simulation_config': asdict(self.config),
            'network_stats': self.network.get_network_stats(),
            'metrics_history': self.metrics_history,
            'performance_data': self.performance_data,
            'export_time': datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2)


if __name__ == "__main__":
    # Test the simulator
    from models.railway_network import create_sample_network
    
    network = create_sample_network()
    config = SimulationConfig(mode=SimulationMode.ACCELERATED, speed_multiplier=10.0)
    simulator = TrainSimulator(network, config)
    
    # Add sample trains
    train1 = Train(
        id="12951",
        route=["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"],
        priority=TrainPriority.EXPRESS,
        scheduled_times=[0, 0.5, 10.5, 14.5, 19.5, 29.5, 34.5, 42.5],
        current_position=0,
        current_time=0.0
    )
    
    train2 = Train(
        id="12952",
        route=["CSTM", "KYN", "NK", "MMR", "BSL", "NGP", "BPL", "JHS", "ALD", "GZB", "DEL"],
        priority=TrainPriority.EXPRESS,
        scheduled_times=[0, 1.0, 3.0, 5.5, 8.5, 12.5, 22.5, 27.5, 31.5, 42.0, 42.5],
        current_position=0,
        current_time=0.0
    )
    
    simulator.add_train(train1)
    simulator.add_train(train2)
    
    # Register event handler
    def print_events(event):
        print(f"Event: {event['type']} at {event['simulation_time']:.1f}min")
    
    simulator.register_event_handler('train_moved', print_events)
    simulator.register_event_handler('train_completed', print_events)
    
    # Run simulation for a short time
    simulator.start_simulation()
    time.sleep(5)  # Run for 5 seconds
    simulator.stop_simulation()
    
    print("Final metrics:", simulator._get_current_metrics())
