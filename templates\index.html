<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚄 AI Train Traffic Control - SIH 2025</title>
    
    <!-- External Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f0f23;
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #ffffff;
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Main Layout */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 2rem;
            min-height: calc(100vh - 100px);
        }

        /* Left Panel */
        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        /* Control Panel */
        .control-panel {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
        }

        .control-panel h3 {
            color: #ffffff;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .control-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        /* Map Container */
        .map-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            flex: 1;
        }

        .map-container h3 {
            color: #ffffff;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
        }

        #map {
            height: 500px;
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        /* Right Panel */
        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
        }

        .card h3 {
            color: #ffffff;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .metric-card {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            display: block;
        }

        .metric-label {
            font-size: 0.85rem;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Train List */
        .train-list {
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .train-list::-webkit-scrollbar {
            width: 6px;
        }

        .train-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .train-list::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .train-item {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .train-item:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(99, 102, 241, 0.5);
            transform: translateX(4px);
        }

        .train-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .train-id {
            font-weight: 600;
            color: #ffffff;
            font-size: 0.95rem;
        }

        .train-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-running {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-delayed {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-stopped {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .train-details {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.4;
        }

        /* Notifications */
        .notifications {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            max-width: 400px;
        }

        .notification {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-left: 4px solid #6366f1;
            animation: slideIn 0.3s ease;
            color: #1f2937;
        }

        .notification.success {
            border-left-color: #10b981;
        }

        .notification.error {
            border-left-color: #ef4444;
        }

        .notification.warning {
            border-left-color: #f59e0b;
        }

        .notification.info {
            border-left-color: #3b82f6;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Speed Control */
        .speed-control {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
        }

        .speed-control label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #ffffff;
            font-size: 0.9rem;
        }

        .speed-control input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            margin-bottom: 0.5rem;
        }

        .speed-control input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #6366f1;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
        }

        .speed-display {
            text-align: center;
            font-weight: 600;
            color: #6366f1;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-train"></i> AI Train Traffic Control</h1>
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="systemStatus">Connecting...</span>
            </div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Left Panel -->
        <div class="left-panel">
            <!-- Control Panel -->
            <div class="control-panel">
                <h3><i class="fas fa-gamepad"></i> Simulation Controls</h3>
                <div class="control-buttons">
                    <button class="btn btn-primary" id="startBtn">
                        <i class="fas fa-play"></i> Start
                    </button>
                    <button class="btn btn-secondary" id="pauseBtn">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="btn btn-danger" id="stopBtn">
                        <i class="fas fa-stop"></i> Stop
                    </button>
                    <button class="btn btn-primary" id="optimizeBtn">
                        <i class="fas fa-brain"></i> AI Optimize
                    </button>
                </div>

                <div class="speed-control">
                    <label for="speedSlider">Simulation Speed:</label>
                    <input type="range" id="speedSlider" min="0.1" max="10" step="0.1" value="1">
                    <div class="speed-display" id="speedDisplay">1.0x</div>
                </div>
            </div>

            <!-- Map -->
            <div class="map-container">
                <h3><i class="fas fa-map"></i> Railway Network</h3>
                <div id="map"></div>
            </div>
        </div>

        <!-- Right Panel -->
        <div class="right-panel">
            <!-- Performance Metrics -->
            <div class="card">
                <h3><i class="fas fa-chart-line"></i> Live Metrics</h3>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="throughputMetric">0</div>
                        <div class="metric-label">Trains/Hour</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="delayMetric">0</div>
                        <div class="metric-label">Avg Delay (min)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="capacityMetric">0</div>
                        <div class="metric-label">Capacity (%)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="optimizationMetric">0</div>
                        <div class="metric-label">AI Time (s)</div>
                    </div>
                </div>
            </div>

            <!-- Train List -->
            <div class="card">
                <h3><i class="fas fa-list"></i> Active Trains</h3>
                <div class="train-list" id="trainList">
                    <div style="text-align: center; color: rgba(255, 255, 255, 0.5); padding: 2rem;">
                        <i class="fas fa-train" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        Start simulation to see trains
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="notifications" id="notifications"></div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Custom JavaScript -->
    <script src="/static/js/app.js"></script>
</body>
</html>
