/**
 * AI-Powered Train Traffic Control System
 * Modern Frontend with Working Simulation
 */

class TrafficControlApp {
    constructor() {
        this.socket = null;
        this.map = null;
        this.trains = {};
        this.trainMarkers = {};
        this.stationMarkers = {};
        this.isConnected = false;
        this.simulationRunning = false;
        this.simulationInterval = null;
        this.metrics = {
            throughput: 0,
            delay: 0,
            capacity: 0,
            optimization: 0
        };
        
        this.init();
    }

    init() {
        console.log('🚄 Initializing AI Train Traffic Control System...');
        this.initializeSocket();
        this.initializeMap();
        this.setupEventListeners();
        this.loadSampleData();
        this.startMetricsUpdate();
    }

    initializeSocket() {
        this.socket = io('/simulation');
        
        this.socket.on('connect', () => {
            console.log('✅ Connected to server');
            this.isConnected = true;
            this.updateSystemStatus('Connected', true);
            this.showNotification('🚀 Connected to AI Traffic Control System', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('❌ Disconnected from server');
            this.isConnected = false;
            this.updateSystemStatus('Disconnected', false);
            this.showNotification('⚠️ Connection lost', 'error');
        });

        this.socket.on('simulation_update', (data) => {
            this.handleSimulationUpdate(data);
        });

        this.socket.on('train_event', (event) => {
            this.handleTrainEvent(event);
        });
    }

    initializeMap() {
        console.log('🗺️ Initializing map...');
        this.map = L.map('map').setView([20.5937, 78.9629], 5);
        
        // Dark theme tiles
        L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(this.map);

        setTimeout(() => {
            this.loadNetworkData();
        }, 500);
    }

    loadNetworkData() {
        console.log('📡 Loading railway network...');
        
        // Delhi-Mumbai corridor stations
        const stations = [
            { id: 'NDLS', name: 'New Delhi', lat: 28.6448, lng: 77.2097, type: 'terminal' },
            { id: 'GZB', name: 'Ghaziabad', lat: 28.6692, lng: 77.4538, type: 'junction' },
            { id: 'AGC', name: 'Agra Cantt', lat: 27.1767, lng: 78.0081, type: 'major' },
            { id: 'JHS', name: 'Jhansi', lat: 25.4484, lng: 78.5685, type: 'junction' },
            { id: 'BPL', name: 'Bhopal', lat: 23.2599, lng: 77.4126, type: 'major' },
            { id: 'NGP', name: 'Nagpur', lat: 21.1458, lng: 79.0882, type: 'major' },
            { id: 'BSL', name: 'Bhusaval', lat: 21.0444, lng: 75.7849, type: 'junction' },
            { id: 'KYN', name: 'Kalyan', lat: 19.2437, lng: 73.1355, type: 'major' },
            { id: 'CSTM', name: 'Mumbai CST', lat: 18.9398, lng: 72.8355, type: 'terminal' }
        ];

        // Add station markers
        stations.forEach(station => {
            const marker = L.circleMarker([station.lat, station.lng], {
                radius: station.type === 'terminal' ? 8 : 6,
                fillColor: station.type === 'terminal' ? '#ef4444' : '#3b82f6',
                color: '#ffffff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            }).addTo(this.map).bindPopup(`
                <div style="color: #333; font-family: Arial;">
                    <h4 style="margin: 0 0 5px 0;">${station.name}</h4>
                    <p style="margin: 0; font-size: 12px;">Code: ${station.id}</p>
                    <p style="margin: 0; font-size: 12px;">Type: ${station.type}</p>
                </div>
            `);
            
            this.stationMarkers[station.id] = marker;
        });

        // Draw railway lines
        for (let i = 0; i < stations.length - 1; i++) {
            L.polyline([
                [stations[i].lat, stations[i].lng],
                [stations[i + 1].lat, stations[i + 1].lng]
            ], {
                color: '#6366f1',
                weight: 3,
                opacity: 0.7
            }).addTo(this.map);
        }

        // Fit map to show all stations
        const group = new L.featureGroup(Object.values(this.stationMarkers));
        this.map.fitBounds(group.getBounds().pad(0.1));
        
        console.log('✅ Railway network loaded');
    }

    loadSampleData() {
        // Sample trains for demonstration
        this.trains = {
            'T001': {
                id: 'T001',
                name: 'Rajdhani Express',
                from: 'NDLS',
                to: 'CSTM',
                status: 'running',
                delay: 0,
                priority: 'EXPRESS',
                position: { lat: 28.6448, lng: 77.2097 },
                progress: 0
            },
            'T002': {
                id: 'T002', 
                name: 'Shatabdi Express',
                from: 'CSTM',
                to: 'NDLS',
                status: 'running',
                delay: 5,
                priority: 'EXPRESS',
                position: { lat: 18.9398, lng: 72.8355 },
                progress: 0
            },
            'T003': {
                id: 'T003',
                name: 'Freight Train',
                from: 'AGC',
                to: 'NGP',
                status: 'delayed',
                delay: 15,
                priority: 'FREIGHT',
                position: { lat: 27.1767, lng: 78.0081 },
                progress: 0
            }
        };

        this.updateTrainDisplay();
        this.addTrainMarkers();
    }

    addTrainMarkers() {
        Object.values(this.trains).forEach(train => {
            const marker = L.marker([train.position.lat, train.position.lng], {
                icon: L.divIcon({
                    className: 'train-marker',
                    html: `<div style="
                        background: ${train.priority === 'EXPRESS' ? '#10b981' : '#f59e0b'};
                        color: white;
                        padding: 2px 6px;
                        border-radius: 12px;
                        font-size: 10px;
                        font-weight: bold;
                        border: 2px solid white;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    ">🚂 ${train.id}</div>`,
                    iconSize: [60, 20],
                    iconAnchor: [30, 10]
                })
            }).addTo(this.map).bindPopup(`
                <div style="color: #333; font-family: Arial;">
                    <h4 style="margin: 0 0 5px 0;">${train.name}</h4>
                    <p style="margin: 0; font-size: 12px;">ID: ${train.id}</p>
                    <p style="margin: 0; font-size: 12px;">Route: ${train.from} → ${train.to}</p>
                    <p style="margin: 0; font-size: 12px;">Status: ${train.status}</p>
                    <p style="margin: 0; font-size: 12px;">Delay: ${train.delay} min</p>
                </div>
            `);
            
            this.trainMarkers[train.id] = marker;
        });
    }

    setupEventListeners() {
        // Start button
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startSimulation();
        });

        // Pause button  
        document.getElementById('pauseBtn').addEventListener('click', () => {
            this.pauseSimulation();
        });

        // Stop button
        document.getElementById('stopBtn').addEventListener('click', () => {
            this.stopSimulation();
        });

        // Optimize button
        document.getElementById('optimizeBtn').addEventListener('click', () => {
            this.runOptimization();
        });
    }

    startSimulation() {
        if (this.simulationRunning) return;
        
        console.log('▶️ Starting simulation...');
        this.simulationRunning = true;
        
        // Update UI
        document.getElementById('startBtn').disabled = true;
        document.getElementById('pauseBtn').disabled = false;
        document.getElementById('stopBtn').disabled = false;
        
        // Start simulation loop
        this.simulationInterval = setInterval(() => {
            this.updateSimulation();
        }, 2000); // Update every 2 seconds
        
        this.showNotification('🚀 Simulation started', 'success');
        
        // Make API call to backend
        fetch('/api/simulation/start', { method: 'POST' })
            .catch(err => console.log('Backend call failed:', err));
    }

    pauseSimulation() {
        if (!this.simulationRunning) return;
        
        console.log('⏸️ Pausing simulation...');
        this.simulationRunning = false;
        
        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }
        
        // Update UI
        document.getElementById('startBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
        
        this.showNotification('⏸️ Simulation paused', 'warning');
        
        fetch('/api/simulation/pause', { method: 'POST' })
            .catch(err => console.log('Backend call failed:', err));
    }

    stopSimulation() {
        console.log('⏹️ Stopping simulation...');
        this.simulationRunning = false;
        
        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }
        
        // Reset UI
        document.getElementById('startBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('stopBtn').disabled = true;
        
        // Reset train positions
        this.loadSampleData();
        
        this.showNotification('⏹️ Simulation stopped', 'error');
        
        fetch('/api/simulation/stop', { method: 'POST' })
            .catch(err => console.log('Backend call failed:', err));
    }

    updateSimulation() {
        // Simulate train movement
        Object.values(this.trains).forEach(train => {
            if (train.status === 'running') {
                // Increase progress
                train.progress += Math.random() * 10 + 5; // 5-15% progress per update
                
                if (train.progress >= 100) {
                    train.progress = 100;
                    train.status = 'completed';
                    this.metrics.throughput += 1;
                }
                
                // Simulate position change (simplified)
                const startLat = train.from === 'NDLS' ? 28.6448 : 18.9398;
                const startLng = train.from === 'NDLS' ? 77.2097 : 72.8355;
                const endLat = train.to === 'CSTM' ? 18.9398 : 28.6448;
                const endLng = train.to === 'CSTM' ? 72.8355 : 77.2097;
                
                const progressRatio = train.progress / 100;
                train.position.lat = startLat + (endLat - startLat) * progressRatio;
                train.position.lng = startLng + (endLng - startLng) * progressRatio;
                
                // Update marker position
                if (this.trainMarkers[train.id]) {
                    this.trainMarkers[train.id].setLatLng([train.position.lat, train.position.lng]);
                }
                
                // Random delay changes
                if (Math.random() < 0.1) { // 10% chance
                    train.delay += Math.random() < 0.5 ? 1 : -1;
                    train.delay = Math.max(0, train.delay);
                }
            }
        });
        
        // Update metrics
        this.metrics.delay = Object.values(this.trains).reduce((sum, train) => sum + train.delay, 0) / Object.keys(this.trains).length;
        this.metrics.capacity = Math.min(95, 60 + Math.random() * 30);
        this.metrics.optimization = 2 + Math.random() * 3;
        
        this.updateTrainDisplay();
        this.updateMetricsDisplay();
    }

    updateTrainDisplay() {
        const trainList = document.getElementById('trainList');
        if (!trainList) return;

        const trainItems = Object.values(this.trains).map(train => `
            <div class="train-item">
                <div class="train-header">
                    <div class="train-id">${train.name}</div>
                    <div class="train-status status-${train.status}">${train.status}</div>
                </div>
                <div class="train-details">
                    ID: ${train.id} | Route: ${train.from} → ${train.to}<br>
                    Priority: ${train.priority} | Delay: ${train.delay} min<br>
                    Progress: ${Math.round(train.progress)}%
                </div>
            </div>
        `).join('');

        trainList.innerHTML = trainItems || `
            <div style="text-align: center; color: rgba(255, 255, 255, 0.5); padding: 2rem;">
                <i class="fas fa-train" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                No active trains
            </div>
        `;
    }

    updateMetricsDisplay() {
        document.getElementById('throughputMetric').textContent = this.metrics.throughput;
        document.getElementById('delayMetric').textContent = this.metrics.delay.toFixed(1);
        document.getElementById('capacityMetric').textContent = Math.round(this.metrics.capacity);
        document.getElementById('optimizationMetric').textContent = this.metrics.optimization.toFixed(1);
    }

    updateSystemStatus(status, isOnline) {
        const statusElement = document.getElementById('systemStatus');
        const statusDot = document.getElementById('statusDot');

        if (statusElement) {
            statusElement.textContent = status;
        }

        if (statusDot) {
            statusDot.style.background = isOnline ? '#10b981' : '#ef4444';
        }
    }

    runOptimization() {
        console.log('🧠 Running AI optimization...');
        this.showNotification('🤖 AI optimization in progress...', 'info');

        // Simulate optimization
        setTimeout(() => {
            // Reduce delays by 20-50%
            Object.values(this.trains).forEach(train => {
                if (train.delay > 0) {
                    train.delay = Math.max(0, Math.round(train.delay * (0.5 + Math.random() * 0.3)));
                }
            });

            this.metrics.optimization = 1 + Math.random() * 2; // Faster optimization
            this.updateTrainDisplay();
            this.updateMetricsDisplay();

            this.showNotification('✅ AI optimization completed - delays reduced!', 'success');
        }, 2000);

        fetch('/api/optimization/run', { method: 'POST' })
            .catch(err => console.log('Backend call failed:', err));
    }

    startMetricsUpdate() {
        // Update metrics every 5 seconds
        setInterval(() => {
            if (this.simulationRunning) {
                // Simulate some metric fluctuations
                this.metrics.capacity += (Math.random() - 0.5) * 5;
                this.metrics.capacity = Math.max(50, Math.min(95, this.metrics.capacity));
                this.updateMetricsDisplay();
            }
        }, 5000);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 4px;">${message}</div>
            <div style="font-size: 0.85em; opacity: 0.8;">${new Date().toLocaleTimeString()}</div>
        `;

        const container = document.getElementById('notifications') || document.body;
        container.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    handleSimulationUpdate(data) {
        console.log('Simulation update:', data);
        // Handle real backend updates if available
    }

    handleTrainEvent(event) {
        console.log('Train event:', event);
        // Handle real train events if available
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Starting AI Train Traffic Control System...');
    window.app = new TrafficControlApp();
});
