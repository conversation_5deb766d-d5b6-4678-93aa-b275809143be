"""
Flask Backend for AI Train Traffic Control System
Real-time API and WebSocket communication
"""

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room, leave_room
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any

# Import our models
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.railway_network import RailwayNetwork, create_sample_network, Station, Track, TrainPriority
from models.ai_optimizer import HybridOptimizer, Train, TrafficState, TrainStatus
from simulation.train_simulator import TrainSimulator, SimulationConfig, SimulationMode

app = Flask(__name__,
            template_folder='../templates',
            static_folder='../static')
app.config['SECRET_KEY'] = 'railway_ai_secret_key_2025'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Global state
network = None
simulator = None
optimizer = None
active_sessions = {}


def initialize_system():
    """Initialize the railway system"""
    global network, simulator, optimizer
    
    # Create sample network
    network = create_sample_network()
    
    # Initialize simulator
    config = SimulationConfig(
        mode=SimulationMode.REAL_TIME,
        speed_multiplier=1.0,
        auto_optimize=True,
        record_metrics=True
    )
    simulator = TrainSimulator(network, config)
    
    # Initialize optimizer
    optimizer = HybridOptimizer(network)
    
    # Register event handlers
    simulator.register_event_handler('simulation_updated', broadcast_simulation_update)
    simulator.register_event_handler('train_moved', broadcast_train_event)
    simulator.register_event_handler('train_delayed', broadcast_train_event)
    simulator.register_event_handler('optimization_completed', broadcast_optimization_result)
    
    print("Railway AI system initialized successfully")


def broadcast_simulation_update(event):
    """Broadcast simulation updates to all connected clients"""
    socketio.emit('simulation_update', event['data'], namespace='/simulation')


def broadcast_train_event(event):
    """Broadcast train events to all connected clients"""
    socketio.emit('train_event', event, namespace='/simulation')


def broadcast_optimization_result(event):
    """Broadcast optimization results to all connected clients"""
    socketio.emit('optimization_result', event['data'], namespace='/simulation')


# REST API Routes

@app.route('/')
def index():
    """Serve the main dashboard"""
    return render_template('index.html')


@app.route('/api/network/info')
def get_network_info():
    """Get railway network information"""
    if not network:
        return jsonify({'error': 'Network not initialized'}), 500
    
    return jsonify({
        'stations': [station.to_dict() for station in network.stations.values()],
        'tracks': [track.to_dict() for track in network.tracks.values()],
        'stats': network.get_network_stats()
    })


@app.route('/api/trains', methods=['GET'])
def get_trains():
    """Get all trains in the system"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    trains_data = {}
    for train_id, train in simulator.traffic_state.trains.items():
        trains_data[train_id] = simulator._serialize_train(train)
    
    return jsonify(trains_data)


@app.route('/api/trains', methods=['POST'])
def add_train():
    """Add a new train to the system"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        data = request.get_json()
        
        train = Train(
            id=data['id'],
            route=data['route'],
            priority=TrainPriority(data['priority']),
            scheduled_times=data['scheduled_times'],
            current_position=data.get('current_position', 0),
            current_time=data.get('current_time', 0.0),
            delay=data.get('delay', 0.0),
            status=TrainStatus(data.get('status', 'scheduled'))
        )
        
        simulator.add_train(train)
        
        return jsonify({
            'success': True,
            'message': f'Train {train.id} added successfully',
            'train': simulator._serialize_train(train)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400


@app.route('/api/simulation/start', methods=['POST'])
def start_simulation():
    """Start the simulation"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.start_simulation()
        return jsonify({'success': True, 'message': 'Simulation started'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/pause', methods=['POST'])
def pause_simulation():
    """Pause/unpause the simulation"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.pause_simulation()
        status = 'paused' if simulator.is_paused else 'running'
        return jsonify({'success': True, 'status': status})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/stop', methods=['POST'])
def stop_simulation():
    """Stop the simulation"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.stop_simulation()
        return jsonify({'success': True, 'message': 'Simulation stopped'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/step', methods=['POST'])
def step_simulation():
    """Step the simulation forward"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.step_simulation()
        return jsonify({'success': True, 'time': simulator.current_time})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/config', methods=['GET', 'POST'])
def simulation_config():
    """Get or update simulation configuration"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    if request.method == 'GET':
        return jsonify({
            'mode': simulator.config.mode.value,
            'speed_multiplier': simulator.config.speed_multiplier,
            'auto_optimize': simulator.config.auto_optimize,
            'show_delays': simulator.config.show_delays,
            'show_capacity': simulator.config.show_capacity,
            'record_metrics': simulator.config.record_metrics
        })
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            if 'mode' in data:
                simulator.config.mode = SimulationMode(data['mode'])
            if 'speed_multiplier' in data:
                simulator.config.speed_multiplier = float(data['speed_multiplier'])
            if 'auto_optimize' in data:
                simulator.config.auto_optimize = bool(data['auto_optimize'])
            if 'show_delays' in data:
                simulator.config.show_delays = bool(data['show_delays'])
            if 'show_capacity' in data:
                simulator.config.show_capacity = bool(data['show_capacity'])
            if 'record_metrics' in data:
                simulator.config.record_metrics = bool(data['record_metrics'])
            
            return jsonify({'success': True, 'message': 'Configuration updated'})
            
        except Exception as e:
            return jsonify({'error': str(e)}), 400


@app.route('/api/optimization/run', methods=['POST'])
def run_optimization():
    """Manually trigger optimization"""
    if not simulator or not optimizer:
        return jsonify({'error': 'System not initialized'}), 500
    
    try:
        result = optimizer.optimize_traffic(simulator.traffic_state)
        return jsonify({
            'success': True,
            'result': result,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/metrics')
def get_metrics():
    """Get current performance metrics"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    return jsonify({
        'current': simulator._get_current_metrics(),
        'history': simulator.metrics_history[-100:],  # Last 100 data points
        'performance': simulator.performance_data
    })


@app.route('/api/scenarios', methods=['GET', 'POST'])
def scenarios():
    """Get or create simulation scenarios"""
    if request.method == 'GET':
        # Return predefined scenarios
        scenarios = [
            {
                'id': 'rush_hour',
                'name': 'Rush Hour Traffic',
                'description': 'High traffic scenario with multiple express trains',
                'train_count': 8
            },
            {
                'id': 'disruption',
                'name': 'Track Disruption',
                'description': 'Single track failure causing delays',
                'train_count': 5
            },
            {
                'id': 'normal',
                'name': 'Normal Operations',
                'description': 'Standard traffic with mixed train types',
                'train_count': 6
            }
        ]
        return jsonify(scenarios)
    
    elif request.method == 'POST':
        # Create custom scenario
        try:
            data = request.get_json()
            # Implementation for creating custom scenarios
            return jsonify({'success': True, 'message': 'Scenario created'})
        except Exception as e:
            return jsonify({'error': str(e)}), 400


# WebSocket Events

@socketio.on('connect', namespace='/simulation')
def handle_connect():
    """Handle client connection"""
    session_id = request.sid
    active_sessions[session_id] = {
        'connected_at': datetime.now().isoformat(),
        'subscriptions': []
    }
    
    emit('connected', {
        'session_id': session_id,
        'server_time': datetime.now().isoformat(),
        'system_status': 'online' if simulator and simulator.is_running else 'offline'
    })
    
    print(f"Client connected: {session_id}")


@socketio.on('disconnect', namespace='/simulation')
def handle_disconnect():
    """Handle client disconnection"""
    session_id = request.sid
    if session_id in active_sessions:
        del active_sessions[session_id]
    
    print(f"Client disconnected: {session_id}")


@socketio.on('subscribe', namespace='/simulation')
def handle_subscribe(data):
    """Handle subscription to specific events"""
    session_id = request.sid
    event_type = data.get('event_type')
    
    if session_id in active_sessions:
        if event_type not in active_sessions[session_id]['subscriptions']:
            active_sessions[session_id]['subscriptions'].append(event_type)
        
        emit('subscription_confirmed', {
            'event_type': event_type,
            'status': 'subscribed'
        })


@socketio.on('unsubscribe', namespace='/simulation')
def handle_unsubscribe(data):
    """Handle unsubscription from events"""
    session_id = request.sid
    event_type = data.get('event_type')
    
    if session_id in active_sessions:
        if event_type in active_sessions[session_id]['subscriptions']:
            active_sessions[session_id]['subscriptions'].remove(event_type)
        
        emit('subscription_confirmed', {
            'event_type': event_type,
            'status': 'unsubscribed'
        })


@socketio.on('get_status', namespace='/simulation')
def handle_get_status():
    """Send current system status"""
    if simulator:
        status = {
            'simulation_running': simulator.is_running,
            'simulation_paused': simulator.is_paused,
            'current_time': simulator.current_time,
            'train_count': len(simulator.traffic_state.trains),
            'metrics': simulator._get_current_metrics()
        }
    else:
        status = {
            'simulation_running': False,
            'error': 'Simulator not initialized'
        }
    
    emit('status_update', status)


# Error Handlers

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500


if __name__ == '__main__':
    # Initialize the system
    initialize_system()
    
    # Add comprehensive sample trains for realistic demo
    sample_trains = [
        # Express Trains
        Train(
            id="12951",
            route=["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 0.5, 10.5, 14.5, 19.5, 29.5, 34.5, 42.5],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="12952",
            route=["CSTM", "KYN", "NK", "MMR", "BSL", "NGP", "BPL", "JHS", "ALD", "GZB", "DEL"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 1.0, 3.0, 5.5, 8.5, 12.5, 22.5, 27.5, 31.5, 42.0, 42.5],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="12953",
            route=["DEL", "GZB", "ALD", "JHS", "BPL", "NGP"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[2.0, 2.5, 12.5, 16.5, 21.5, 31.5],
            current_position=0,
            current_time=0.0
        ),
        # Passenger Trains
        Train(
            id="19037",
            route=["DEL", "GZB", "ALD", "JHS", "BPL"],
            priority=TrainPriority.PASSENGER,
            scheduled_times=[1.0, 1.5, 11.5, 15.5, 20.5],
            current_position=0,
            current_time=0.0,
            delay=3.0  # Start with some delay
        ),
        Train(
            id="19038",
            route=["BPL", "JHS", "ALD", "GZB", "DEL"],
            priority=TrainPriority.PASSENGER,
            scheduled_times=[0, 5.0, 9.0, 19.0, 19.5],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="19039",
            route=["NGP", "BSL", "MMR", "NK", "KYN", "CSTM"],
            priority=TrainPriority.PASSENGER,
            scheduled_times=[0, 4.5, 8.5, 10.5, 15.5, 16.0],
            current_position=0,
            current_time=0.0
        ),
        # Freight Trains
        Train(
            id="50001",
            route=["DEL", "GZB", "ALD", "JHS"],
            priority=TrainPriority.FREIGHT,
            scheduled_times=[3.0, 3.5, 13.5, 17.5],
            current_position=0,
            current_time=0.0,
            delay=8.0  # Freight typically has more delays
        ),
        Train(
            id="50002",
            route=["NGP", "BSL", "MMR", "NK"],
            priority=TrainPriority.FREIGHT,
            scheduled_times=[1.0, 5.5, 9.5, 11.5],
            current_position=0,
            current_time=0.0,
            delay=5.0
        ),
        # Maintenance Train
        Train(
            id="87001",
            route=["BPL", "NGP", "BSL"],
            priority=TrainPriority.MAINTENANCE,
            scheduled_times=[4.0, 14.0, 18.5],
            current_position=0,
            current_time=0.0
        )
    ]
    
    for train in sample_trains:
        simulator.add_train(train)
    
    print("Starting Flask server...")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
